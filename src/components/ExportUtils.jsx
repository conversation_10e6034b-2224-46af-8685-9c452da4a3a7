import { PDFDocument, rgb } from 'pdf-lib'

export const useExportUtils = (allPdfData, currentPdfIndex, originalPdfDimensions, canvasToPdfCoordinates) => {
  
  // Export annotations as JSON (convert canvas coordinates to PDF coordinates)
  const exportAnnotations = (annotations) => {
    // Get all annotations for current PDF
    const currentPdfAnnotations = []
    Object.keys(annotations).forEach(key => {
      const [pdfIndex, pageIndex] = key.split('-').map(Number)
      if (pdfIndex === currentPdfIndex) {
        annotations[key].forEach(annotation => {
          currentPdfAnnotations.push({
            ...annotation,
            pageIndex: pageIndex
          })
        })
      }
    })

    const exportData = {
      pdfName: allPdfData.current[currentPdfIndex]?.name || 'unknown.pdf',
      pdfDimensions: originalPdfDimensions.current,
      annotations: currentPdfAnnotations.map(annotation => {
        if (annotation.type === 'rectangle') {
          // Calculate all four corner points in PDF coordinates
          const pdfTopLeft = canvasToPdfCoordinates(annotation.x, annotation.y)
          const pdfTopRight = canvasToPdfCoordinates(annotation.x + annotation.width, annotation.y)
          const pdfBottomLeft = canvasToPdfCoordinates(annotation.x, annotation.y + annotation.height)
          const pdfBottomRight = canvasToPdfCoordinates(annotation.x + annotation.width, annotation.y + annotation.height)

          // Calculate normalized coordinates (0-1 range) for portability
          const pdfDimensions = originalPdfDimensions.current
          const normalizedCoords = {
            x: pdfTopLeft.x / pdfDimensions.width,
            y: pdfTopLeft.y / pdfDimensions.height,
            width: Math.abs(pdfBottomRight.x - pdfTopLeft.x) / pdfDimensions.width,
            height: Math.abs(pdfBottomRight.y - pdfTopLeft.y) / pdfDimensions.height
          }

          return {
            id: annotation.id,
            type: annotation.type,
            pageIndex: annotation.pageIndex,
            coordinates: {
              // PDF coordinates in points (absolute)
              pdf: {
                x: pdfTopLeft.x,
                y: pdfTopLeft.y,
                width: Math.abs(pdfBottomRight.x - pdfTopLeft.x),
                height: Math.abs(pdfBottomRight.y - pdfTopLeft.y)
              },
              // Normalized coordinates (0-1 range) for portability
              normalized: normalizedCoords,
              // Legacy format for backward compatibility
              x: pdfTopLeft.x,
              y: pdfTopLeft.y,
              width: Math.abs(pdfBottomRight.x - pdfTopLeft.x),
              height: Math.abs(pdfBottomRight.y - pdfTopLeft.y),
              // All four corner points in PDF coordinates
              points: {
                topLeft: pdfTopLeft,
                topRight: pdfTopRight,
                bottomLeft: pdfBottomLeft,
                bottomRight: pdfBottomRight
              }
            },
            color: annotation.color,
            // Include assigned names
            label: annotation.label,
            roomName: annotation.roomName,
            roomPath: annotation.roomPath
          }
        } else if (annotation.type === 'polygon') {
          // Convert all polygon points to PDF coordinates
          const pdfPoints = annotation.points.map(point => canvasToPdfCoordinates(point.x, point.y))

          // Calculate normalized points (0-1 range) for portability
          const pdfDimensions = originalPdfDimensions.current
          const normalizedPoints = pdfPoints.map(point => ({
            x: point.x / pdfDimensions.width,
            y: point.y / pdfDimensions.height
          }))

          return {
            id: annotation.id,
            type: annotation.type,
            pageIndex: annotation.pageIndex,
            coordinates: {
              // PDF coordinates in points (absolute)
              pdf: {
                points: pdfPoints
              },
              // Normalized coordinates (0-1 range) for portability
              normalized: {
                points: normalizedPoints
              },
              // Legacy format for backward compatibility
              points: pdfPoints
            },
            color: annotation.color,
            // Include assigned names
            label: annotation.label,
            roomName: annotation.roomName,
            roomPath: annotation.roomPath
          }
        }
        return annotation
      })
    }

    const pdfName = allPdfData.current[currentPdfIndex]?.name.replace('.pdf', '') || 'annotations'
    const blob = new Blob([JSON.stringify(exportData, null, 2)], { type: 'application/json' })
    const url = URL.createObjectURL(blob)
    const a = document.createElement('a')
    a.href = url
    a.download = `${pdfName}_annotations.json`
    document.body.appendChild(a)
    a.click()
    document.body.removeChild(a)
    URL.revokeObjectURL(url)
  }

  // Export annotated PDF with annotations drawn on it
  const exportAnnotatedPDF = async (annotations) => {
    if (allPdfData.current.length === 0) {
      alert('No PDF file loaded')
      return
    }

    // Get all annotations for current PDF
    const currentPdfAnnotations = []
    Object.keys(annotations).forEach(key => {
      const [pdfIndex, pageIndex] = key.split('-').map(Number)
      if (pdfIndex === currentPdfIndex) {
        annotations[key].forEach(annotation => {
          currentPdfAnnotations.push({
            ...annotation,
            pageIndex: pageIndex
          })
        })
      }
    })

    if (currentPdfAnnotations.length === 0) {
      alert('No annotations to export')
      return
    }

    try {
      // Read the original PDF file
      const currentPdfFile = allPdfData.current[currentPdfIndex].file
      const arrayBuffer = await currentPdfFile.arrayBuffer()
      const pdfDoc = await PDFDocument.load(arrayBuffer)
      const pages = pdfDoc.getPages()

      // Group annotations by page
      const annotationsByPage = {}
      currentPdfAnnotations.forEach(annotation => {
        if (!annotationsByPage[annotation.pageIndex]) {
          annotationsByPage[annotation.pageIndex] = []
        }
        annotationsByPage[annotation.pageIndex].push(annotation)
      })

      // Draw annotations on each page
      Object.keys(annotationsByPage).forEach(pageIndex => {
        const pageNum = parseInt(pageIndex)
        if (pageNum < pages.length) {
          const page = pages[pageNum]
          const { height } = page.getSize()

          annotationsByPage[pageIndex].forEach(annotation => {
            // Convert annotation color to RGB
            const color = annotation.color === '#ff0000' ? rgb(1, 0, 0) : rgb(0, 0, 1)

            if (annotation.type === 'rectangle') {
              // Convert canvas coordinates to PDF coordinates
              const pdfTopLeft = canvasToPdfCoordinates(annotation.x, annotation.y)
              const pdfBottomRight = canvasToPdfCoordinates(annotation.x + annotation.width, annotation.y + annotation.height)

              // PDF coordinate system has origin at bottom-left, so we need to flip Y
              const x = pdfTopLeft.x
              const y = height - pdfBottomRight.y  // Flip Y and use bottom-right Y
              const rectWidth = pdfBottomRight.x - pdfTopLeft.x
              const rectHeight = pdfBottomRight.y - pdfTopLeft.y

              page.drawRectangle({
                x: x,
                y: y,
                width: rectWidth,
                height: rectHeight,
                borderColor: color,
                borderWidth: 2,
              })
            } else if (annotation.type === 'polygon' && annotation.points.length > 2) {
              // Draw polygon by connecting lines
              for (let i = 0; i < annotation.points.length; i++) {
                const currentCanvasPoint = annotation.points[i]
                const nextCanvasPoint = annotation.points[(i + 1) % annotation.points.length]

                // Convert canvas coordinates to PDF coordinates
                const currentPdfPoint = canvasToPdfCoordinates(currentCanvasPoint.x, currentCanvasPoint.y)
                const nextPdfPoint = canvasToPdfCoordinates(nextCanvasPoint.x, nextCanvasPoint.y)

                page.drawLine({
                  start: { x: currentPdfPoint.x, y: height - currentPdfPoint.y },
                  end: { x: nextPdfPoint.x, y: height - nextPdfPoint.y },
                  thickness: 2,
                  color: color,
                })
              }
            }
          })
        }
      })

      // Save the annotated PDF
      const pdfBytes = await pdfDoc.save()
      const blob = new Blob([pdfBytes], { type: 'application/pdf' })
      const url = URL.createObjectURL(blob)
      const a = document.createElement('a')
      a.href = url
      a.download = 'annotated_document.pdf'
      document.body.appendChild(a)
      a.click()
      document.body.removeChild(a)
      URL.revokeObjectURL(url)
    } catch (error) {
      console.error('Error creating annotated PDF:', error)
      alert('Error creating annotated PDF. Please try again.')
    }
  }

  return {
    exportAnnotations,
    exportAnnotatedPDF
  }
}
